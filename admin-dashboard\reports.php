<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';


// Require authentication
Auth::requireLogin();

$currentUser = Auth::getCurrentUser();
$reportModel = new Report();

// Get dashboard statistics for sidebar
$stats = $reportModel->generateDashboardStats();

// Handle report generation requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'generate_report') {
        try {
            $reportType = $_POST['report_type'];
            $dateRange = $_POST['date_range'] ?? '30';
            
            $reportData = $reportModel->generateCustomReport($reportType, $dateRange);
            
            // Save report to database
            $reportName = ucfirst(str_replace('_', ' ', $reportType)) . " Report - " . date('Y-m-d H:i:s');
            $reportModel->saveReport($reportName, $reportData, $currentUser['user_id']);
            
            $success = "Report generated successfully!";
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Handle success/error messages from URL parameters
$success = $_GET['success'] ?? null;
$error = $_GET['error'] ?? null;

// Get recent reports
$recentReports = $reportModel->getRecentReports(10);

// Get financial summary for last 30 days
$financialData = $reportModel->generateCustomReport('financial_summary', 30);

$quoteData = $reportModel->generateCustomReport('quote_summary', 30);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports & Analytics - Meleva Admin</title>
    <link rel="stylesheet" href="../dist/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar-transition {
            transition: all 0.3s ease;
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #f97316, #ea580c);
        }
        
        .gradient-blue {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }
        
        .gradient-green {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        
        .gradient-purple {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }
        
        .gradient-red {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .gradient-yellow {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        
        .sidebar-active {
            background-color: rgba(249, 115, 22, 0.1);
            border-right: 3px solid #f97316;
        }

        .stats-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .chart-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            padding: 24px;
            transition: all 0.3s ease;
            max-height: 350px;
            overflow: hidden;
        }

        .chart-container:hover {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Ensure chart canvas doesn't grow beyond container */
        .chart-container canvas {
            max-height: 250px !important;
            width: 100% !important;
        }
        
        .report-section {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }
        
        .section-header {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            padding: 20px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .metric-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .badge-success {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
        }
        
        .badge-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #d97706;
        }
        
        .badge-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
        }
        
        .badge-info {
            background: rgba(59, 130, 246, 0.1);
            color: #2563eb;
        }
        
        .progress-bar {
            height: 8px;
            background: #f3f4f6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background: #f9fafb;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #6b7280;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .filter-tabs {
            display: flex;
            background: #f3f4f6;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 24px;
        }
        
        .filter-tab {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #6b7280;
        }
        
        .filter-tab.active {
            background: white;
            color: #f97316;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* Mobile and Tablet Responsive Styles */
        @media (max-width: 1023px) {
            #sidebar {
                transform: translateX(-100%);
            }
            
            #sidebar.sidebar-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
        
        /* Hide toggle button on desktop */
        @media (min-width: 1024px) {
            #sidebar-toggle {
                display: none;
            }
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .chart-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    
    <!-- Sidebar -->
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Reports & Analytics</h1>
            </div>
        </div>

        <!-- Content Area -->
        <div class="p-6"><main class="p-6">
            <!-- Page Description and Actions -->
            <div class="flex items-center justify-between mb-6 flex-wrap gap-4">
                <div>
                    <p class="text-gray-600">Comprehensive insights and data analysis</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <button onclick="toggleExportDropdown()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center">
                            <i class="fas fa-download mr-2"></i>Export
                            <i class="fas fa-chevron-down ml-2 text-sm"></i>
                        </button>
                        <div id="exportDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <a href="#" onclick="exportToPDF()" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-t-lg">
                                <i class="fas fa-file-pdf mr-2 text-red-500"></i>Export as PDF
                            </a>
                            <a href="#" onclick="exportToExcel()" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-b-lg">
                                <i class="fas fa-file-excel mr-2 text-green-500"></i>Export as Excel
                            </a>
                        </div>
                    </div>
                    <button onclick="refreshData()" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>
            </div>

            <?php if (isset($success)): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-check-circle mr-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Financial Metrics Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 stats-grid">
                <!-- Total Revenue -->
                <div class="stats-card card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600 mb-1">Total Revenue (30 days)</p>
                                <p class="text-3xl font-bold text-gray-900">$<?php echo number_format($financialData['data']['total_revenue'] ?? 0, 0); ?></p>
                                <div class="flex items-center mt-2">
                                    <span class="metric-badge badge-success">
                                        <i class="fas fa-dollar-sign mr-1"></i><?php echo $financialData['data']['total_payments'] ?? 0; ?> payments
                                    </span>
                                </div>
                            </div>
                            <div class="stats-icon gradient-bg">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tour Packages -->
                <div class="stats-card card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600 mb-1">Tour Packages</p>
                                <p class="text-3xl font-bold text-gray-900"><?php echo $stats['tour_packages'] ?? 0; ?></p>
                                <div class="flex items-center mt-2">
                                    <span class="metric-badge badge-info">
                                        <i class="fas fa-suitcase mr-1"></i>Available packages
                                    </span>
                                </div>
                            </div>
                            <div class="stats-icon gradient-blue">
                                <i class="fas fa-suitcase"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quote Conversion -->
                <div class="stats-card card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600 mb-1">Quote Conversion (30 days)</p>
                                <p class="text-3xl font-bold text-gray-900"><?php echo number_format($quoteData['data']['conversion_rate'] ?? 0, 1); ?>%</p>
                                <div class="flex items-center mt-2">
                                    <span class="metric-badge badge-info">
                                        <i class="fas fa-percentage mr-1"></i><?php echo $quoteData['data']['accepted_quotes'] ?? 0; ?> accepted
                                    </span>
                                </div>
                            </div>
                            <div class="stats-icon gradient-green">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pending Payments -->
                <div class="stats-card card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600 mb-1">Pending Payments</p>
                                <p class="text-3xl font-bold text-gray-900"><?php echo $financialData['data']['pending_payments'] ?? 0; ?></p>
                                <div class="flex items-center mt-2">
                                    <?php if (($financialData['data']['pending_payments'] ?? 0) > 5): ?>
                                        <span class="metric-badge badge-warning">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>High
                                        </span>
                                    <?php else: ?>
                                        <span class="metric-badge badge-success">
                                            <i class="fas fa-check mr-1"></i>Normal
                                        </span>
                                    <?php endif; ?>
                                    <span class="text-xs text-gray-500 ml-2">Priority level</span>
                                </div>
                            </div>
                            <div class="stats-icon gradient-purple">
                                <i class="fas fa-envelope"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Period Filter -->
            <div class="filter-tabs">
                <div class="filter-tab active" onclick="switchTimeFilter('7d')">7 Days</div>
                <div class="filter-tab" onclick="switchTimeFilter('30d')">30 Days</div>
                <div class="filter-tab" onclick="switchTimeFilter('90d')">90 Days</div>
                <div class="filter-tab" onclick="switchTimeFilter('1y')">1 Year</div>
            </div>

            <!-- Charts Section -->
            <div class="mb-6">
                <!-- Business Performance Chart -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-lg font-semibold text-gray-800">Business Performance Overview</h3>
                        <span class="metric-badge badge-success">
                            <i class="fas fa-chart-line mr-1"></i>Current Status
                        </span>
                    </div>
                    <div style="height: 250px;">
                        <canvas id="businessChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Financial Reports Section -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- Payment Statistics -->
                <div class="report-section">
                    <div class="section-header">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-credit-card mr-2"></i>Payment Statistics
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Total Revenue</span>
                                <span class="font-semibold">$<?php echo number_format($financialData['data']['total_revenue'] ?? 0, 0); ?></span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill gradient-bg" style="width: 85%"></div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Completed Payments</span>
                                <span class="font-semibold"><?php echo $financialData['data']['total_payments'] ?? 0; ?></span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill gradient-green" style="width: 70%"></div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Pending Payments</span>
                                <span class="font-semibold"><?php echo $financialData['data']['pending_payments'] ?? 0; ?></span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill gradient-yellow" style="width: <?php echo min(($financialData['data']['pending_payments'] ?? 0) * 10, 100); ?>%"></div>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Average Payment</span>
                                <span class="font-semibold">$<?php echo number_format($financialData['data']['avg_payment'] ?? 0, 0); ?></span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill gradient-blue" style="width: 60%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Statistics -->
                <div class="report-section">
                    <div class="section-header">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-chart-bar mr-2"></i>Content Statistics
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Total Destinations</span>
                                <span class="font-semibold"><?php echo $stats['destinations'] ?? 0; ?></span>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Tour Packages</span>
                                <span class="metric-badge badge-success">
                                    <i class="fas fa-suitcase mr-1"></i><?php echo $stats['tour_packages'] ?? 0; ?>
                                </span>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Total Messages</span>
                                <span class="font-semibold"><?php echo $stats['messages'] ?? 0; ?></span>
                            </div>

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Unread Messages</span>
                                <span class="metric-badge badge-warning">
                                    <i class="fas fa-envelope mr-1"></i><?php echo $stats['unread_messages'] ?? 0; ?>
                                </span>
                            </div>

                            <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                                <div class="text-xs text-gray-600 mb-1">Quote Conversion Rate</div>
                                <div class="flex items-center">
                                    <div class="progress-bar flex-1 mr-3">
                                        <div class="progress-fill gradient-green" style="width: <?php echo min($quoteData['data']['conversion_rate'] ?? 0, 100); ?>%"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-green-600"><?php echo number_format($quoteData['data']['conversion_rate'] ?? 0, 1); ?>%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="report-section">
                    <div class="section-header">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-bolt mr-2"></i>Generate Reports
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <button onclick="generateReport('financial_summary')" class="w-full bg-orange-50 hover:bg-orange-100 text-orange-700 px-4 py-3 rounded-lg text-left transition-colors">
                                <i class="fas fa-dollar-sign mr-2"></i>Financial Summary
                            </button>



                            <button onclick="generateReport('quote_summary')" class="w-full bg-green-50 hover:bg-green-100 text-green-700 px-4 py-3 rounded-lg text-left transition-colors">
                                <i class="fas fa-file-invoice mr-2"></i>Quote Summary
                            </button>

                            <a href="payments.php" class="w-full bg-purple-50 hover:bg-purple-100 text-purple-700 px-4 py-3 rounded-lg text-left transition-colors block">
                                <i class="fas fa-credit-card mr-2"></i>View All Payments
                            </a>


                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Reports Table -->
            <div class="report-section">
                <div class="section-header">
                    <h3 class="text-lg font-semibold flex items-center">
                        <i class="fas fa-history mr-2"></i>Recent Reports
                    </h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Report Name</th>
                                <th>Generated By</th>
                                <th>Date Created</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($recentReports)): ?>
                                <?php foreach ($recentReports as $report): ?>
                                    <tr>
                                        <td class="font-medium text-gray-900"><?php echo htmlspecialchars($report['report_name']); ?></td>
                                        <td><?php echo htmlspecialchars($report['username'] ?? 'System'); ?></td>
                                        <td><?php echo date('M j, Y g:i A', strtotime($report['generated_at'])); ?></td>
                                        <td>
                                            <span class="metric-badge badge-info">
                                                <i class="fas fa-chart-bar mr-1"></i>Analytics
                                            </span>
                                        </td>
                                        <td>
                                            <span class="metric-badge badge-success">
                                                <i class="fas fa-check mr-1"></i>Complete
                                            </span>
                                        </td>
                                        <td>
                                            <div class="flex items-center space-x-2">
                                                <button onclick="viewReport(<?php echo $report['report_id']; ?>)" class="text-blue-600 hover:text-blue-800 transition-colors">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button onclick="downloadReport(<?php echo $report['report_id']; ?>)" class="text-green-600 hover:text-green-800 transition-colors">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button onclick="deleteReport(<?php echo $report['report_id']; ?>)" class="text-red-600 hover:text-red-800 transition-colors">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center py-8 text-gray-500">
                                        <i class="fas fa-chart-bar text-4xl mb-4 opacity-50"></i>
                                        <p>No reports generated yet</p>
                                        <p class="text-sm">Generate your first report using the quick actions above</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Generate Custom Report Form -->
            <div class="report-section mt-6">
                <div class="section-header">
                    <h3 class="text-lg font-semibold flex items-center">
                        <i class="fas fa-cog mr-2"></i>Generate Custom Report
                    </h3>
                </div>
                <div class="p-6">
                    <form method="POST" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <input type="hidden" name="action" value="generate_report">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                            <select name="report_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                <option value="financial_summary">Financial Summary</option>

                                <option value="quote_summary">Quote Summary</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                            <select name="date_range" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="365">Last Year</option>
                            </select>
                        </div>
                        
                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-chart-line mr-2"></i>Generate Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sidebar toggle functionality
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            
            sidebar.classList.toggle('sidebar-open');
            overlay.classList.toggle('active');
        });

        document.getElementById('sidebar-overlay').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            
            sidebar.classList.remove('sidebar-open');
            overlay.classList.remove('active');
        });

        // Chart initialization
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });

        function initializeCharts() {
            // Business Performance Chart
            const businessCtx = document.getElementById('businessChart').getContext('2d');
            new Chart(businessCtx, {
                type: 'bar',
                data: {
                    labels: ['Quotes', 'Destinations', 'Payments', 'Revenue ($)'],
                    datasets: [{
                        label: 'Current Status',
                        data: [
                            <?php echo $stats['total_quotes'] ?? 0; ?>,
                            <?php echo $stats['destinations'] ?? 0; ?>,
                            <?php echo $stats['total_payments'] ?? 0; ?>,
                            <?php echo intval($stats['total_revenue'] ?? 0); ?>
                        ],
                        backgroundColor: [
                            'rgba(249, 115, 22, 0.8)',  // Orange for quotes
                            'rgba(59, 130, 246, 0.8)',   // Blue for destinations
                            'rgba(16, 185, 129, 0.8)',   // Green for payments
                            'rgba(139, 92, 246, 0.8)'    // Purple for revenue
                        ],
                        borderColor: [
                            '#f97316',
                            '#3b82f6',
                            '#10b981',
                            '#8b5cf6'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 0 // Disable animations to prevent growth
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: Math.max(50, Math.max(
                                <?php echo $stats['total_quotes'] ?? 0; ?>,
                                <?php echo $stats['destinations'] ?? 0; ?>,
                                <?php echo $stats['total_payments'] ?? 0; ?>,
                                <?php echo intval($stats['total_revenue'] ?? 0); ?>
                            ) * 1.2), // Set max to prevent growth
                            ticks: {
                                stepSize: 1
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 0,
                                minRotation: 0
                            }
                        }
                    },
                    layout: {
                        padding: {
                            top: 5,
                            bottom: 5,
                            left: 5,
                            right: 5
                        }
                    }
                }
            });
        }

        // Filter functions
        function switchTimeFilter(period) {
            // Remove active class from all tabs
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Add active class to clicked tab
            event.target.classList.add('active');
            
            // Update charts with new data (implement as needed)
            console.log('Switching to period:', period);
        }

        // Report generation functions
        function generateReport(type) {
            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...';
            button.disabled = true;
            
            // Simulate report generation
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                
                // Show success message
                showNotification('Report generated successfully!', 'success');
            }, 2000);
        }

        // Export dropdown functions
        function toggleExportDropdown() {
            const dropdown = document.getElementById('exportDropdown');
            dropdown.classList.toggle('hidden');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('exportDropdown');
            const button = event.target.closest('button');
            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleExportDropdown') === -1) {
                dropdown.classList.add('hidden');
            }
        });

        function exportToPDF() {
            document.getElementById('exportDropdown').classList.add('hidden');
            showNotification('Generating PDF report...', 'info');

            // Create form and submit for PDF export
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'export_report.php';

            const formatInput = document.createElement('input');
            formatInput.type = 'hidden';
            formatInput.name = 'format';
            formatInput.value = 'pdf';
            form.appendChild(formatInput);

            const typeInput = document.createElement('input');
            typeInput.type = 'hidden';
            typeInput.name = 'report_type';
            typeInput.value = 'financial_summary';
            form.appendChild(typeInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportToExcel() {
            document.getElementById('exportDropdown').classList.add('hidden');
            showNotification('Generating Excel report...', 'info');

            // Create form and submit for Excel export
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'export_report.php';

            const formatInput = document.createElement('input');
            formatInput.type = 'hidden';
            formatInput.name = 'format';
            formatInput.value = 'excel';
            form.appendChild(formatInput);

            const typeInput = document.createElement('input');
            typeInput.type = 'hidden';
            typeInput.name = 'report_type';
            typeInput.value = 'financial_summary';
            form.appendChild(typeInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function refreshData() {
            showNotification('Refreshing data...', 'info');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        function viewReport(reportId) {
            showNotification('Loading report details...', 'info');

            // Create and submit form to view report details
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'view_report.php';
            form.target = '_blank';

            const reportIdInput = document.createElement('input');
            reportIdInput.type = 'hidden';
            reportIdInput.name = 'report_id';
            reportIdInput.value = reportId;
            form.appendChild(reportIdInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function downloadReport(reportId) {
            showNotification('Preparing report download...', 'info');

            // Create and submit form to download existing report
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'download_saved_report.php';

            const reportIdInput = document.createElement('input');
            reportIdInput.type = 'hidden';
            reportIdInput.name = 'report_id';
            reportIdInput.value = reportId;
            form.appendChild(reportIdInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function deleteReport(reportId) {
            if (confirm('Are you sure you want to delete this report? This action cannot be undone.')) {
                showNotification('Deleting report...', 'warning');

                // Create and submit form to delete report
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'delete_report.php';

                const reportIdInput = document.createElement('input');
                reportIdInput.type = 'hidden';
                reportIdInput.name = 'report_id';
                reportIdInput.value = reportId;
                form.appendChild(reportIdInput);

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete';
                form.appendChild(actionInput);

                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);
            }
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 transition-all duration-300 transform translate-x-full`;
            
            switch(type) {
                case 'success':
                    notification.classList.add('bg-green-600');
                    break;
                case 'error':
                    notification.classList.add('bg-red-600');
                    break;
                case 'warning':
                    notification.classList.add('bg-yellow-600');
                    break;
                default:
                    notification.classList.add('bg-blue-600');
            }
            
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-info-circle mr-2"></i>
                    ${message}
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>

