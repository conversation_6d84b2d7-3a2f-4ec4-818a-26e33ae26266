<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';

// Require authentication
Auth::requireLogin();

$packageTypeModel = new TourPackageType();
$currentUser = Auth::getCurrentUser();

// Get dashboard statistics for sidebar
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                $data = [
                    'type_name' => Utils::sanitizeInput($_POST['type_name']),
                    'description' => Utils::sanitizeInput($_POST['description'])
                ];
                
                if ($packageTypeModel->create($data)) {
                    $success = 'Package type created successfully!';
                } else {
                    $error = 'Failed to create package type.';
                }
                break;
                
            case 'update':
                $id = intval($_POST['id']);
                $data = [
                    'type_name' => Utils::sanitizeInput($_POST['type_name']),
                    'description' => Utils::sanitizeInput($_POST['description'])
                ];
                
                if ($packageTypeModel->update($id, $data)) {
                    $success = 'Package type updated successfully!';
                } else {
                    $error = 'Failed to update package type.';
                }
                break;
                
            case 'delete':
                $id = intval($_POST['id']);
                if ($packageTypeModel->delete($id)) {
                    $success = 'Package type deleted successfully!';
                } else {
                    $error = 'Failed to delete package type.';
                }
                break;
        }
    }
}

// Get all package types
$packageTypes = $packageTypeModel->findAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Package Types - Meleva Admin</title>
    <link rel="stylesheet" href="../dist/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        .sidebar-transition { transition: all 0.3s ease; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); }
        .gradient-bg { background: linear-gradient(135deg, #f97316, #ea580c); }
        .sidebar-active { background-color: rgba(249, 115, 22, 0.1); border-right: 3px solid #f97316; }

        .form-header {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px 12px 0 0;
            margin: -1px -1px 0 -1px;
            transition: all 0.3s ease;
        }

        .form-header:hover {
            background: linear-gradient(135deg, #ea580c, #dc2626);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
        }

        .form-section {
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background: white;
        }

        .collapsible-content.expanded {
            max-height: 1000px;
            transition: max-height 0.5s ease-in;
        }
        
        /* Mobile and Tablet Responsive Styles */
        @media (max-width: 1023px) {
            #sidebar {
                transform: translateX(-100%);
            }
            
            #sidebar.sidebar-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
        
        /* Hide toggle button on desktop */
        @media (min-width: 1024px) {
            #sidebar-toggle {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    
    <!-- Sidebar -->
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Package Types</h1>
            </div>
        </div>

        <!-- Content Area -->
        <div class="p-6">
            <?php if (isset($success)): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-check-circle mr-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Add Package Type Form (Inline) -->
            <div class="form-section mb-6">
                <div class="form-header cursor-pointer" id="toggleAddFormHeader">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-plus mr-2"></i>Add New Package Type
                        </h3>
                        <div class="text-white hover:text-orange-200 transition-colors">
                            <i class="fas fa-chevron-down transform transition-transform" id="toggleIcon"></i>
                        </div>
                    </div>
                </div>

                <div id="addFormContent" class="collapsible-content">
                    <div class="p-6">
                        <form id="addPackageTypeForm" method="POST">
                            <input type="hidden" name="action" value="create">

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Type Name</label>
                                    <input type="text" name="type_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500" placeholder="e.g., Adventure Tours, Cultural Tours">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                    <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500" placeholder="Brief description of this package type"></textarea>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                                <button type="button" onclick="resetAddForm()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                    Reset
                                </button>
                                <button type="submit" class="px-6 py-2 text-white gradient-bg rounded-lg hover:opacity-90 transition-opacity">
                                    <i class="fas fa-plus mr-2"></i>Create Package Type
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Package Types Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php foreach ($packageTypes as $type): ?>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 card-hover">
                        <div class="flex items-start justify-between mb-4">
                            <div class="w-12 h-12 gradient-bg rounded-lg flex items-center justify-center">
                                <i class="fas fa-tag text-white text-xl"></i>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="editPackageType(<?php echo $type['package_type_id']; ?>)" class="text-orange-600 hover:text-orange-800">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deletePackageType(<?php echo $type['package_type_id']; ?>)" class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($type['type_name']); ?></h3>
                        
                        <?php if ($type['description']): ?>
                            <p class="text-gray-600 text-sm"><?php echo htmlspecialchars($type['description']); ?></p>
                        <?php else: ?>
                            <p class="text-gray-400 text-sm italic">No description provided</p>
                        <?php endif; ?>
                        
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <p class="text-xs text-gray-500">
                                Created: <?php echo Utils::formatDate($type['created_at']); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
                
                <?php if (empty($packageTypes)): ?>
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-tags text-gray-300 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No package types yet</h3>
                        <p class="text-gray-500 mb-4">Create your first package type to organize tour packages.</p>
                        <button onclick="showAddModal()" class="gradient-bg text-white px-4 py-2 rounded-lg hover:opacity-90 transition-opacity">
                            <i class="fas fa-plus mr-2"></i>Add Package Type
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Add/Edit Modal -->
    <div id="packageTypeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Add Package Type</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="packageTypeForm" method="POST">
                        <input type="hidden" name="action" id="formAction" value="create">
                        <input type="hidden" name="id" id="packageTypeId">
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Type Name</label>
                                <input type="text" name="type_name" id="typeName" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="e.g., Safari Tours">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                <textarea name="description" id="description" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="Describe this package type..."></textarea>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 mt-6">
                            <button type="button" onclick="closeModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                                Cancel
                            </button>
                            <button type="submit" class="px-4 py-2 text-white gradient-bg rounded-lg hover:opacity-90">
                                <span id="submitText">Create Package Type</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Mobile sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            
            // Toggle sidebar on mobile
            function toggleSidebar() {
                sidebar.classList.toggle('sidebar-open');
                sidebarOverlay.classList.toggle('active');
                document.body.classList.toggle('overflow-hidden');
            }
            
            // Close sidebar
            function closeSidebar() {
                sidebar.classList.remove('sidebar-open');
                sidebarOverlay.classList.remove('active');
                document.body.classList.remove('overflow-hidden');
            }
            
            // Event listeners
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }
            
            // Close sidebar when clicking on navigation links on mobile
            const navLinks = sidebar.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth < 1024) {
                        closeSidebar();
                    }
                });
            });
            
            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    closeSidebar();
                }
            });

            // Initialize form toggle functionality
            initializeFormToggle();
        });

        // Form toggle functionality
        function initializeFormToggle() {
            const toggleHeader = document.getElementById('toggleAddFormHeader');
            const formContent = document.getElementById('addFormContent');
            const toggleIcon = document.getElementById('toggleIcon');

            if (toggleHeader && formContent && toggleIcon) {
                toggleHeader.addEventListener('click', function() {
                    formContent.classList.toggle('expanded');
                    toggleIcon.classList.toggle('rotate-180');
                });
            }
        }

        // Reset add form
        function resetAddForm() {
            document.getElementById('addPackageTypeForm').reset();
        }

        // Package type management (decode HTML entities for proper display)
        const packageTypes = <?php
            // Decode HTML entities in the data before JSON encoding
            $decodedPackageTypes = array_map(function($packageType) {
                if (isset($packageType['type_name'])) {
                    $packageType['type_name'] = html_entity_decode($packageType['type_name'], ENT_QUOTES, 'UTF-8');
                }
                if (isset($packageType['description'])) {
                    $packageType['description'] = html_entity_decode($packageType['description'], ENT_QUOTES, 'UTF-8');
                }
                return $packageType;
            }, $packageTypes);
            echo json_encode($decodedPackageTypes, JSON_UNESCAPED_UNICODE | JSON_HEX_QUOT | JSON_HEX_APOS);
        ?>;
        
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'Add Package Type';
            document.getElementById('formAction').value = 'create';
            document.getElementById('submitText').textContent = 'Create Package Type';
            document.getElementById('packageTypeForm').reset();
            document.getElementById('packageTypeModal').classList.remove('hidden');
        }
        
        function editPackageType(id) {
            const packageType = packageTypes.find(pt => pt.package_type_id == id);
            if (packageType) {
                document.getElementById('modalTitle').textContent = 'Edit Package Type';
                document.getElementById('formAction').value = 'update';
                document.getElementById('submitText').textContent = 'Update Package Type';
                document.getElementById('packageTypeId').value = packageType.package_type_id;
                document.getElementById('typeName').value = packageType.type_name;
                document.getElementById('description').value = packageType.description || '';
                document.getElementById('packageTypeModal').classList.remove('hidden');
            }
        }
        
        function deletePackageType(id) {
            if (confirm('Are you sure you want to delete this package type?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function closeModal() {
            document.getElementById('packageTypeModal').classList.add('hidden');
        }
    </script>
</body>
</html>