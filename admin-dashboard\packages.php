<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';
require_once 'classes/ImageManager.php';

// Require authentication
Auth::requireLogin();

$tourPackageModel = new TourPackage();
$packageTypeModel = new TourPackageType();
$currentUser = Auth::getCurrentUser();

// Get dashboard statistics for sidebar
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                try {
                    $data = [
                        'package_type_id' => intval($_POST['package_type_id']),
                        'destination_id' => !empty($_POST['destination_id']) ? intval($_POST['destination_id']) : null,
                        'name' => Utils::sanitizeInput($_POST['name']),
                        'description' => Utils::sanitizeInput($_POST['description']),
                        'itinerary' => !empty($_POST['itinerary']) ? Utils::sanitizeInput($_POST['itinerary']) : null,
                        'price' => floatval($_POST['price']),
                        'duration' => Utils::sanitizeInput($_POST['duration']),
                        'is_featured' => 0, // Always create new packages as regular (not featured)
                        'created_by_user_id' => $currentUser['user_id']
                    ];

                    error_log('Final data array: ' . print_r($data, true));
                    
                    $packageId = $tourPackageModel->create($data);
                    if ($packageId) {
                        $uploadedImages = [];

                        // Handle multiple image uploads using ImageManager
                        if (isset($_FILES["package_images"]) && !empty($_FILES["package_images"]["name"][0])) {
                            $imageManager = new ImageManager('upload/images/packages/', 'tour_packages');
                            $displayImageIndex = isset($_POST['display_image_index']) ? intval($_POST['display_image_index']) : null;

                            $uploadResult = $imageManager->processMultipleUploadsForPackages(
                                $_FILES["package_images"],
                                $packageId,
                                $currentUser['user_id'],
                                Utils::sanitizeInput($_POST['name']),
                                $displayImageIndex
                            );

                            if ($uploadResult['success']) {
                                $uploadedImages = $uploadResult['uploaded_images'];

                                // Update tour package with display image
                                if ($uploadResult['display_image_id']) {
                                    $currentPackage = $tourPackageModel->findById($packageId);
                                    if ($currentPackage) {
                                        $updateData = [
                                            'package_type_id' => $currentPackage['package_type_id'],
                                            'name' => $currentPackage['name'],
                                            'description' => $currentPackage['description'],
                                            'price' => $currentPackage['price'],
                                            'duration' => $currentPackage['duration'],
                                            'is_featured' => $currentPackage['is_featured'] ?? 0,
                                            'display_image_id' => $uploadResult['display_image_id']
                                        ];
                                        $tourPackageModel->update($packageId, $updateData);
                                    }
                                }

                                $success = "Tour package created successfully with " . count($uploadedImages) . " image(s)!";

                                if (!empty($uploadResult['errors'])) {
                                    $success .= " Some files had issues: " . implode(', ', $uploadResult['errors']);
                                }
                            } else {
                                $error = "Tour package created but image upload failed: " . implode(', ', $uploadResult['errors']);
                            }
                        } else {
                            $success = "Tour package created successfully (no images uploaded).";
                        }
                    } else {
                        $error = "Failed to create tour package.";
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;
                
            case 'update':
                try {
                    $id = intval($_POST['id']);
                    $data = [
                        'package_type_id' => intval($_POST['package_type_id']),
                        'destination_id' => !empty($_POST['destination_id']) ? intval($_POST['destination_id']) : null,
                        'name' => Utils::sanitizeInput($_POST['name']),
                        'description' => Utils::sanitizeInput($_POST['description']),
                        'itinerary' => !empty($_POST['itinerary']) ? Utils::sanitizeInput($_POST['itinerary']) : null,
                        'price' => floatval($_POST['price']),
                        'duration' => Utils::sanitizeInput($_POST['duration']),
                        'is_featured' => isset($_POST['is_featured']) ? 1 : 0
                    ];

                    $uploadedImages = [];

                    // Handle new image uploads for update using ImageManager
                    if (isset($_FILES['package_images']) && !empty($_FILES['package_images']['name'][0])) {
                        $imageManager = new ImageManager('upload/images/packages/', 'tour_packages');
                        $displayImageIndex = isset($_POST['display_image_index']) ? intval($_POST['display_image_index']) : null;

                        $uploadResult = $imageManager->processMultipleUploadsForPackages(
                            $_FILES['package_images'],
                            $id,
                            $currentUser['user_id'],
                            Utils::sanitizeInput($_POST['name']),
                            $displayImageIndex
                        );

                        if ($uploadResult['success']) {
                            $uploadedImages = $uploadResult['uploaded_images'];

                            // Update tour package with display image if new one was set
                            if ($uploadResult['display_image_id']) {
                                $data['display_image_id'] = $uploadResult['display_image_id'];
                            }
                        } else {
                            $error = 'Failed to upload new images: ' . implode(', ', $uploadResult['errors']);
                        }
                    }

                    // Handle display image selection from existing images
                    if (isset($_POST['display_image_id']) && !empty($_POST['display_image_id']) && empty($uploadedImages)) {
                        $displayImageId = intval($_POST['display_image_id']);
                        $imageManager = new ImageManager('upload/images/packages/', 'tour_packages');

                        if ($imageManager->setDisplayImageForPackage($id, $displayImageId)) {
                            $data['display_image_id'] = $displayImageId;
                        }
                    }

                    // Get current package data to preserve display_image_id if not being updated
                    $currentPackage = $tourPackageModel->findById($id);
                    if ($currentPackage && !isset($data['display_image_id'])) {
                        $data['display_image_id'] = $currentPackage['display_image_id'];
                    }

                    if ($tourPackageModel->update($id, $data)) {
                        $success = 'Tour package updated successfully!';
                        if (!empty($uploadedImages)) {
                            $success .= ' Added ' . count($uploadedImages) . ' new image(s).';
                        }
                    } else {
                        $error = 'Failed to update tour package.';
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;
                
            case 'delete':
                $id = intval($_POST['id']);
                if ($tourPackageModel->delete($id)) {
                    $success = 'Tour package deleted successfully!';
                } else {
                    $error = 'Failed to delete tour package.';
                }
                break;

            case 'set_display_image':
                $imageId = intval($_POST['image_id']);
                $tourPackageId = intval($_POST['tour_package_id']);

                $imageManager = new ImageManager('upload/images/packages/', 'tour_packages');
                $result = $imageManager->setDisplayImageForPackage($tourPackageId, $imageId);

                if ($result) {
                    echo json_encode(['success' => true]);
                } else {
                    echo json_encode(['success' => false, 'error' => 'Failed to set display image']);
                }
                exit;

            case 'delete_image':
                $imageId = intval($_POST['image_id']);
                $tourPackageId = intval($_POST['tour_package_id']);

                $imageManager = new ImageManager('upload/images/packages/', 'tour_packages');
                $result = $imageManager->deleteImageForPackage($imageId, $tourPackageId);

                if ($result['success']) {
                    echo json_encode(['success' => true]);
                } else {
                    echo json_encode(['success' => false, 'error' => $result['error']]);
                }
                exit;
        }
    }
}

// Get all tour packages with details
$packages = $tourPackageModel->findAllWithDetails();

// Get all package types for dropdown
$packageTypes = $packageTypeModel->findAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tour Packages - Meleva Admin</title>
    <link rel="stylesheet" href="../dist/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        .sidebar-transition { transition: all 0.3s ease; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); }
        .gradient-bg { background: linear-gradient(135deg, #f97316, #ea580c); }
        .sidebar-active { background-color: rgba(249, 115, 22, 0.1); border-right: 3px solid #f97316; }
        
        /* Image Upload Styles */
        .upload-zone {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }

        .upload-zone.dragover {
            border-color: #f97316;
            background-color: rgba(249, 115, 22, 0.05);
        }

        .image-preview {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .image-preview:hover {
            transform: scale(1.02);
        }

        .image-preview img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-preview:hover .image-overlay {
            opacity: 1;
        }

        .compression-info {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .display-image-indicator {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #f97316;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Form Styles */
        .form-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
        }

        .form-header {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px 12px 0 0;
            margin: -1px -1px 0 -1px;
            transition: all 0.3s ease;
        }

        .form-header:hover {
            background: linear-gradient(135deg, #ea580c, #dc2626);
            transform: translateY(-1px);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .collapsible-content.expanded {
            max-height: 2000px;
        }
        
        /* Mobile and Tablet Responsive Styles */
        @media (max-width: 1023px) {
            #sidebar {
                transform: translateX(-100%);
            }
            
            #sidebar.sidebar-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
        
        /* Hide toggle button on desktop */
        @media (min-width: 1024px) {
            #sidebar-toggle {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    
    <!-- Sidebar -->
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Tour Packages</h1>
            </div>
        </div>

        <!-- Content Area -->
        <div class="p-6"><main class="p-6">
            <?php if (isset($success)): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-check-circle mr-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Add Package Form (Inline) -->
            <div class="form-section mb-6">
                <div class="form-header cursor-pointer" id="toggleAddFormHeader">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-plus mr-2"></i>Add New Tour Package
                        </h3>
                        <div class="text-white hover:text-orange-200 transition-colors">
                            <i class="fas fa-chevron-down transform transition-transform" id="toggleIcon"></i>
                        </div>
                    </div>
                </div>

                <div id="addFormContent" class="collapsible-content">
                    <div class="p-6">
                        <form id="addPackageForm" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="create">
                            <input type="hidden" name="display_image_index" id="addDisplayImageIndex" value="">

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Left Column - Form Fields -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Package Name *</label>
                                        <input type="text" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="e.g., Maasai Mara Safari Adventure">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Package Type *</label>
                                        <select name="package_type_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                            <option value="">Select Package Type</option>
                                            <?php foreach ($packageTypes as $type): ?>
                                                <option value="<?php echo $type['package_type_id']; ?>"><?php echo htmlspecialchars($type['type_name']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Destination</label>
                                        <select name="destination_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                            <option value="">Select Destination (Optional)</option>
                                            <?php
                                            $destinationModel = new Destination();
                                            $destinations = $destinationModel->findAll();
                                            foreach ($destinations as $destination): ?>
                                                <option value="<?php echo $destination['destination_id']; ?>">
                                                    <?php echo htmlspecialchars($destination['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <p class="text-xs text-gray-500 mt-1">Link this package to a specific destination for better organization</p>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Price ($) *</label>
                                            <input type="number" name="price" step="0.01" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="0.00">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                                            <input type="text" name="duration" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="e.g., 3 Days 2 Nights (optional for Day Tours)">
                                            <p class="text-xs text-gray-500 mt-1">Leave empty for package types where duration is implied (e.g., Day Tour, Overnight Safari)</p>
                                        </div>
                                    </div>

                                    <div>
                                        <p class="text-sm text-gray-600 bg-blue-50 border border-blue-200 rounded-lg p-3">
                                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                            <strong>Note:</strong> New packages are created as "Regular" by default. You can mark a package as "Featured" after creation using the edit button.
                                        </p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                                        <textarea name="description" rows="6" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="Describe the tour package details, inclusions, activities, etc."></textarea>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Itinerary (Optional)</label>
                                        <textarea name="itinerary" id="addItinerary" rows="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="Add detailed day-by-day itinerary, activities, schedule, etc."></textarea>
                                        <p class="text-xs text-gray-500 mt-1">Use the rich text editor to format your itinerary with headings, lists, and styling</p>
                                    </div>
                                </div>

                                <!-- Right Column - Image Upload -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Upload Images</label>

                                        <!-- Upload Zone -->
                                        <div class="upload-zone border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-orange-500 transition-colors">
                                            <div class="space-y-2">
                                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400"></i>
                                                <p class="text-sm text-gray-600">
                                                    <span class="font-medium text-orange-600">Click to upload</span> or drag and drop
                                                </p>
                                                <p class="text-xs text-gray-500">PNG, JPG, GIF, WebP (auto-compressed to max 5MB)</p>
                                            </div>
                                            <input type="file" id="addImageInput" name="package_images[]" multiple accept="image/*" class="hidden">
                                        </div>

                                        <!-- Compression Progress -->
                                        <div id="addCompressionProgress" class="hidden mt-2">
                                            <div class="bg-gray-200 rounded-full h-2">
                                                <div id="addProgressBar" class="bg-orange-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-1">Compressing images...</p>
                                        </div>
                                    </div>

                                    <!-- Image Previews -->
                                    <div id="addImagePreviewContainer" class="space-y-4">
                                        <div id="addImagePreviewGrid" class="grid grid-cols-2 gap-3"></div>
                                    </div>

                                    <!-- Upload Summary -->
                                    <div id="addUploadSummary" class="hidden bg-blue-50 p-3 rounded-lg">
                                        <div class="flex items-center justify-between text-sm">
                                            <span class="text-blue-700">Total files: <span id="addTotalFiles">0</span></span>
                                            <span class="text-blue-700">Total size: <span id="addTotalSize">0 KB</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                                <button type="button" onclick="resetAddForm()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                    Reset
                                </button>
                                <button type="submit" class="px-6 py-2 text-white gradient-bg rounded-lg hover:opacity-90 transition-opacity">
                                    <i class="fas fa-plus mr-2"></i>Create Package
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Packages Table -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden card-hover">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">All Tour Packages</h3>
                    <p class="text-sm text-gray-500 mt-1">Manage your tour packages and offerings</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Package</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Featured</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php if (empty($packages)): ?>
                                <tr>
                                    <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                        <i class="fas fa-suitcase-rolling text-3xl mb-2"></i>
                                        <p>No tour packages found. Create your first package above!</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($packages as $package): ?>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex flex-col items-center justify-center">
                                                <?php if ($package['display_image_url']): ?>
                                                    <img class="h-16 w-16 rounded-lg object-cover border border-gray-200 shadow-sm" src="<?php echo htmlspecialchars($package['display_image_url']); ?>" alt="<?php echo htmlspecialchars(Utils::displayText($package['name'])); ?>" title="Display image for <?php echo htmlspecialchars(Utils::displayText($package['name'])); ?>">
                                                <?php else: ?>
                                                    <div class="h-16 w-16 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center shadow-sm mb-1">
                                                        <i class="fas fa-suitcase-rolling text-white text-xl"></i>
                                                    </div>
                                                    <div class="text-xs text-gray-400 text-center">
                                                        <i class="fas fa-image mr-1"></i>No display image
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900"><?php echo Utils::displayText($package['name']); ?></div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                <?php echo Utils::displayText($package['type_name'] ?? 'No Type'); ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-semibold text-gray-900">$<?php echo number_format($package['price'], 2); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                <?php
                                                if (!empty($package['duration'])) {
                                                    echo Utils::displayText($package['duration']);
                                                } else {
                                                    echo '<span class="text-gray-400 italic">Implied in type</span>';
                                                }
                                                ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($package['is_featured']): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    <i class="fas fa-star mr-1"></i>Featured
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    Regular
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><?php echo Utils::displayText($package['created_by'] ?? 'Unknown'); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button onclick="editPackage(<?php echo $package['tour_package_id']; ?>)" class="text-orange-600 hover:text-orange-900 mr-3 transition-colors" title="Edit package">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button onclick="deletePackage(<?php echo $package['tour_package_id']; ?>)" class="text-red-600 hover:text-red-900 transition-colors" title="Delete package">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-edit mr-2 text-orange-600"></i>Edit Tour Package
                        </h3>
                        <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form id="editPackageForm" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="id" id="editPackageId">
                        <input type="hidden" name="display_image_index" id="editDisplayImageIndex" value="">

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Left Column - Form Fields -->
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Package Name *</label>
                                    <input type="text" name="name" id="editPackageName" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Package Type *</label>
                                    <select name="package_type_id" id="editPackageTypeId" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                        <option value="">Select Package Type</option>
                                        <?php foreach ($packageTypes as $type): ?>
                                            <option value="<?php echo $type['package_type_id']; ?>"><?php echo htmlspecialchars($type['type_name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Destination</label>
                                    <select name="destination_id" id="editPackageDestinationId" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                        <option value="">Select Destination (Optional)</option>
                                        <?php foreach ($destinations as $destination): ?>
                                            <option value="<?php echo $destination['destination_id']; ?>">
                                                <?php echo htmlspecialchars($destination['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-1">Link this package to a specific destination</p>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Price ($) *</label>
                                        <input type="number" name="price" id="editPackagePrice" step="0.01" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                                        <input type="text" name="duration" id="editPackageDuration" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="Optional for Day Tours">
                                        <p class="text-xs text-gray-500 mt-1">Leave empty for package types where duration is implied</p>
                                    </div>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3 cursor-pointer">
                                        <input type="checkbox" name="is_featured" id="editPackageFeatured" value="1" class="w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-2">
                                        <span class="text-sm font-medium text-gray-700">
                                            <i class="fas fa-star text-orange-500 mr-1"></i>
                                            Feature this package on homepage
                                        </span>
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1 ml-7">Featured packages appear in the homepage carousel (max 7 packages)</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                                    <textarea name="description" id="editPackageDescription" rows="6" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Itinerary (Optional)</label>
                                    <textarea name="itinerary" id="editItinerary" rows="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="Add detailed day-by-day itinerary, activities, schedule, etc."></textarea>
                                    <p class="text-xs text-gray-500 mt-1">Use the rich text editor to format your itinerary with headings, lists, and styling</p>
                                </div>
                            </div>

                            <!-- Right Column - Image Upload -->
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Add New Images</label>

                                    <!-- Upload Zone -->
                                    <div class="upload-zone border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-orange-500 transition-colors">
                                        <div class="space-y-2">
                                            <i class="fas fa-cloud-upload-alt text-2xl text-gray-400"></i>
                                            <p class="text-sm text-gray-600">
                                                <span class="font-medium text-orange-600">Click to upload</span> new images
                                            </p>
                                            <p class="text-xs text-gray-500">PNG, JPG, GIF, WebP (auto-compressed to max 5MB)</p>
                                        </div>
                                        <input type="file" id="editImageInput" name="package_images[]" multiple accept="image/*" class="hidden">
                                    </div>

                                    <!-- Compression Progress -->
                                    <div id="editCompressionProgress" class="hidden mt-2">
                                        <div class="bg-gray-200 rounded-full h-2">
                                            <div id="editProgressBar" class="bg-orange-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                        </div>
                                        <p class="text-xs text-gray-600 mt-1">Compressing images...</p>
                                    </div>
                                </div>

                                <!-- New Image Previews -->
                                <div id="editImagePreviewContainer" class="space-y-4">
                                    <div id="editImagePreviewGrid" class="grid grid-cols-2 gap-3"></div>

                                    <!-- Upload Summary -->
                                    <div id="editUploadSummary" class="hidden bg-blue-50 p-3 rounded-lg">
                                        <div class="flex items-center justify-between text-sm">
                                            <span class="text-blue-700">New files: <span id="editTotalFiles">0</span></span>
                                            <span class="text-blue-700">Total size: <span id="editTotalSize">0 KB</span></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Existing Images -->
                                <div id="existingImagesContainer" class="hidden">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Existing Images</h4>
                                    <div id="existingImagesGrid" class="grid grid-cols-2 gap-3"></div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                            <button type="button" onclick="closeEditModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                Cancel
                            </button>
                            <button type="submit" class="px-6 py-2 text-white gradient-bg rounded-lg hover:opacity-90 transition-opacity">
                                <i class="fas fa-save mr-2"></i>Update Package
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let addSelectedFiles = [];
        let editSelectedFiles = [];
        let existingImages = [];
        let addDisplayImageIndex = -1;
        let editDisplayImageIndex = -1;

        // Package data for JavaScript (decode HTML entities for proper display)
        const packages = <?php
            // Decode HTML entities in the data before JSON encoding
            $decodedPackages = array_map(function($package) {
                if (isset($package['name'])) {
                    $package['name'] = html_entity_decode($package['name'], ENT_QUOTES, 'UTF-8');
                }
                if (isset($package['description'])) {
                    $package['description'] = html_entity_decode($package['description'], ENT_QUOTES, 'UTF-8');
                }
                return $package;
            }, $packages);
            echo json_encode($decodedPackages, JSON_UNESCAPED_UNICODE | JSON_HEX_QUOT | JSON_HEX_APOS);
        ?>;

        // Mobile sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            // Toggle sidebar on mobile
            function toggleSidebar() {
                sidebar.classList.toggle('sidebar-open');
                sidebarOverlay.classList.toggle('active');
                document.body.classList.toggle('overflow-hidden');
            }

            // Close sidebar
            function closeSidebar() {
                sidebar.classList.remove('sidebar-open');
                sidebarOverlay.classList.remove('active');
                document.body.classList.remove('overflow-hidden');
            }

            // Event listeners
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }

            // Close sidebar when clicking on navigation links on mobile
            const navLinks = sidebar.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth < 1024) {
                        closeSidebar();
                    }
                });
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    closeSidebar();
                }
            });

            // Initialize form functionality
            initializeAddForm();
            initializeEditForm();
            initializeFormToggle();
        });

        // Form toggle functionality
        function initializeFormToggle() {
            const toggleHeader = document.getElementById('toggleAddFormHeader');
            const formContent = document.getElementById('addFormContent');
            const toggleIcon = document.getElementById('toggleIcon');

            toggleHeader.addEventListener('click', function() {
                const isExpanding = !formContent.classList.contains('expanded');

                formContent.classList.toggle('expanded');
                toggleIcon.classList.toggle('rotate-180');

                // Initialize form when expanding (opening) but don't reset user input
                if (isExpanding) {
                    initializeAddFormState();
                }
            });
        }

        // Initialize add form state without resetting user input (like edit form)
        function initializeAddFormState() {
            console.log('initializeAddFormState called');

            // Only reset file-related state, not form fields
            addSelectedFiles = [];
            addDisplayImageIndex = -1;
            document.getElementById('addImagePreviewGrid').innerHTML = '';
            document.getElementById('addUploadSummary').classList.add('hidden');
            document.getElementById('addDisplayImageIndex').value = '';

            // Don't reset the featured checkbox or other form fields
            // Let the user's input persist
            console.log('Add form state initialized without resetting user input');
        }

        // Reset add form to default state (only called when explicitly requested)
        function resetAddForm() {
            console.log('resetAddForm called - explicit reset requested');
            const form = document.getElementById('addPackageForm');
            if (form) {
                form.reset();

                // Reset file-related variables
                addSelectedFiles = [];
                addDisplayImageIndex = -1;
                document.getElementById('addImagePreviewGrid').innerHTML = '';
                document.getElementById('addUploadSummary').classList.add('hidden');
                document.getElementById('addDisplayImageIndex').value = '';

                console.log('Add form completely reset');
            } else {
                console.error('Add form not found');
            }
        }

        // Add form initialization
        function initializeAddForm() {
            console.log('initializeAddForm called');

            // Get form reference outside setTimeout to avoid scope issues
            const form = document.getElementById('addPackageForm');
            console.log('Form found (outside timeout):', form);

            // Wait a bit to ensure DOM is fully loaded
            setTimeout(() => {
                const uploadZone = document.querySelector('#addFormContent .upload-zone');
                const imageInput = document.getElementById('addImageInput');

                console.log('Upload zone found:', uploadZone);
                console.log('Image input found:', imageInput);

                if (!uploadZone || !imageInput) {
                    console.error('Required elements not found for add form initialization');
                    return;
                }

                // Remove any existing event listeners to prevent duplicates
                const newUploadZone = uploadZone.cloneNode(true);
                uploadZone.parentNode.replaceChild(newUploadZone, uploadZone);

                console.log('Cleaned upload zone, adding fresh event listener');

                // Upload zone click handler
                newUploadZone.addEventListener('click', (e) => {
                    console.log('Upload zone clicked');
                    console.log('Image input element:', imageInput);
                    console.log('Attempting to trigger file input click...');

                    try {
                        imageInput.click();
                        console.log('File input click triggered successfully');
                    } catch (error) {
                        console.error('Error triggering file input click:', error);
                    }
                });

                // File input change handler
                imageInput.addEventListener('change', (e) => {
                    console.log('File input changed, files selected:', e.target.files.length);
                    handleFileSelection(e, 'add');
                });

                // Drag and drop handlers
                newUploadZone.addEventListener('dragover', handleDragOver);
                newUploadZone.addEventListener('dragleave', handleDragLeave);
                newUploadZone.addEventListener('drop', (e) => handleFileDrop(e, 'add'));

            }, 100); // End of setTimeout

            // Form is ready for submission - no special handling needed for featured checkbox
            // since new packages are always created as regular (not featured)
        }

        // Edit form initialization
        function initializeEditForm() {
            const uploadZone = document.querySelector('#editModal .upload-zone');
            const imageInput = document.getElementById('editImageInput');

            // Upload zone click handler
            uploadZone.addEventListener('click', () => imageInput.click());

            // File input change handler
            imageInput.addEventListener('change', (e) => handleFileSelection(e, 'edit'));

            // Drag and drop handlers
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('dragleave', handleDragLeave);
            uploadZone.addEventListener('drop', (e) => handleFileDrop(e, 'edit'));
        }

        // Package management functions
        function editPackage(id) {
            const package = packages.find(p => p.tour_package_id == id);
            if (package) {
                document.getElementById('editPackageId').value = package.tour_package_id;
                document.getElementById('editPackageName').value = package.name;
                document.getElementById('editPackageTypeId').value = package.package_type_id || '';
                document.getElementById('editPackageDestinationId').value = package.destination_id || '';
                document.getElementById('editPackagePrice').value = package.price;
                document.getElementById('editPackageDuration').value = package.duration;
                document.getElementById('editPackageDescription').value = package.description || '';

                // Set itinerary content in Quill editor
                setTimeout(function() {
                    if (typeof editQuill !== 'undefined' && editQuill) {
                        editQuill.root.innerHTML = package.itinerary || '';
                        document.getElementById('editItinerary').value = package.itinerary || '';
                        console.log('✅ Itinerary content loaded into Quill editor');
                    } else {
                        document.getElementById('editItinerary').value = package.itinerary || '';
                        console.log('⚠️ Quill editor not available, using textarea');
                    }
                }, 100);

                document.getElementById('editPackageFeatured').checked = package.is_featured == 1;

                // Reset form state
                editSelectedFiles = [];
                editDisplayImageIndex = -1;
                document.getElementById('editImagePreviewGrid').innerHTML = '';
                document.getElementById('editUploadSummary').classList.add('hidden');

                // Load existing images (if any)
                loadExistingImages(id);

                document.getElementById('editModal').classList.remove('hidden');
            }
        }

        function deletePackage(id) {
            const package = packages.find(p => p.tour_package_id == id);
            const packageName = package ? package.name : 'this package';

            if (confirm(`Are you sure you want to delete "${packageName}"? This action cannot be undone.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function closeEditModal() {
            document.getElementById('editModal').classList.add('hidden');
        }



        // File selection handler
        function handleFileSelection(event, context) {
            const files = Array.from(event.target.files);
            processFiles(files, context);
        }

        // Process selected files with enhanced compression
        function processFiles(files, context) {
            console.log('processFiles called with context:', context, 'files:', files.length);

            // Filter image files
            const imageFiles = files.filter(file => file.type.startsWith('image/'));

            if (imageFiles.length === 0) {
                alert('Please select valid image files.');
                return;
            }

            console.log('Filtered image files:', imageFiles.length);

            if (context === 'add') {
                // Get the current length to calculate starting index for new files
                const startIndex = addSelectedFiles.length;
                // Append new files to existing ones instead of replacing
                addSelectedFiles = [...addSelectedFiles, ...imageFiles];
                console.log('Total files after adding:', addSelectedFiles.length);
                // Process only the newly added files
                compressAndPreviewNewFiles(imageFiles, startIndex, 'add');
            } else {
                // Get the current length to calculate starting index for new files
                const startIndex = editSelectedFiles.length;
                // For edit context, append new files to existing ones
                editSelectedFiles = [...editSelectedFiles, ...imageFiles];
                console.log('Total files after adding:', editSelectedFiles.length);
                // Process only the newly added files
                compressAndPreviewNewFiles(imageFiles, startIndex, 'edit');
            }
        }

        // Process only newly added files without clearing existing previews
        async function compressAndPreviewNewFiles(newFiles, startIndex, context) {
            const progressContainer = document.getElementById(context + 'CompressionProgress');
            const progressBar = document.getElementById(context + 'ProgressBar');
            const previewGrid = document.getElementById(context + 'ImagePreviewGrid');

            console.log('Starting compression for new files, context:', context, 'startIndex:', startIndex);
            console.log('Progress container found:', progressContainer);
            console.log('Progress bar found:', progressBar);

            if (!progressContainer || !progressBar) {
                console.error('Progress elements not found for context:', context);
                return;
            }

            progressContainer.classList.remove('hidden');

            const compressedFiles = [];
            const maxSizeBytes = 5 * 1024 * 1024; // 5MB

            try {
                for (let i = 0; i < newFiles.length; i++) {
                    const file = newFiles[i];
                    const actualIndex = startIndex + i; // Use actual index in the full array

                    // Show progress as we start processing each file
                    const startProgress = (i / newFiles.length) * 100;
                    const endProgress = ((i + 1) / newFiles.length) * 100;
                    progressBar.style.width = `${startProgress}%`;

                    try {
                        // Compress with multiple quality levels until under 5MB
                        let compressedFile = await compressImageToSize(file, maxSizeBytes);

                        if (compressedFile) {
                            compressedFiles.push({
                                original: file,
                                compressed: compressedFile,
                                index: actualIndex // Use actual index for proper management
                            });

                            createImagePreview(compressedFile, file, actualIndex, context);

                            // Update progress to show completion of this file
                            console.log(`Completed file ${i + 1}/${newFiles.length} - ${endProgress}%`);
                            progressBar.style.width = `${endProgress}%`;
                        } else {
                            throw new Error('Compression returned null');
                        }
                    } catch (error) {
                        console.error('Error compressing image:', error);

                        // If compression fails, still try to use original if under 5MB
                        if (file.size <= maxSizeBytes) {
                            compressedFiles.push({
                                original: file,
                                compressed: file,
                                index: actualIndex
                            });
                            createImagePreview(file, file, actualIndex, context);
                            console.log(`Using original file for ${file.name} (compression failed but file is under size limit)`);
                        } else {
                            const errorMsg = error.message.includes('timeout')
                                ? `Image compression timed out for ${file.name}. Please try a smaller image or different format.`
                                : `Failed to compress ${file.name} to required size (${formatFileSize(maxSizeBytes)}). Current size: ${formatFileSize(file.size)}. Please choose a smaller image.`;

                            alert(errorMsg);
                            console.error(`Compression failed for ${file.name}:`, error);
                        }
                    }
                }
            } catch (error) {
                console.error('Error in compression process:', error);
                alert('An error occurred during image processing. Please try again.');
            } finally {
                // Always hide progress bar when done, even if there's an error
                console.log('Finally block reached - hiding progress container for context:', context);

                // Use setTimeout to ensure progress bar hides after async operations complete
                setTimeout(() => {
                    if (progressContainer) {
                        progressContainer.classList.add('hidden');
                        console.log('Progress container successfully hidden for context:', context);
                    } else {
                        console.error('Progress container not found when trying to hide for context:', context);
                    }
                }, 500); // Increased timeout to 500ms
            }

            // Update the form data with all files (existing + new)
            updateFormDataWithAllFiles(context);
            updateUploadSummary(getAllCompressedFiles(context), context);
        }

        // Enhanced compression and preview with guaranteed 5MB max (legacy function for full refresh)
        async function compressAndPreviewFiles(files, context) {
            const progressContainer = document.getElementById(context + 'CompressionProgress');
            const progressBar = document.getElementById(context + 'ProgressBar');
            const previewGrid = document.getElementById(context + 'ImagePreviewGrid');

            console.log('Starting compression for context:', context);
            console.log('Progress container found:', progressContainer);
            console.log('Progress bar found:', progressBar);

            if (!progressContainer || !progressBar) {
                console.error('Progress elements not found for context:', context);
                return;
            }

            progressContainer.classList.remove('hidden');

            // Don't clear the preview grid - we want to append new images
            // previewGrid.innerHTML = '';

            const compressedFiles = [];
            const maxSizeBytes = 5 * 1024 * 1024; // 5MB

            // Get the current number of existing previews to adjust indices
            const existingPreviews = previewGrid.children.length;

            try {
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    // Show progress as we start processing each file
                    const startProgress = (i / files.length) * 100;
                    const endProgress = ((i + 1) / files.length) * 100;

                    console.log(`Starting file ${i + 1}/${files.length} - ${startProgress}%`);
                    progressBar.style.width = `${startProgress}%`;

                    try {
                        // Compress with multiple quality levels until under 5MB
                        let compressedFile = await compressImageToSize(file, maxSizeBytes);

                        if (compressedFile) {
                            compressedFiles.push({
                                original: file,
                                compressed: compressedFile,
                                index: i
                            });

                            createImagePreview(compressedFile, file, i, context);

                            // Update progress to show completion of this file
                            console.log(`Completed file ${i + 1}/${files.length} - ${endProgress}%`);
                            progressBar.style.width = `${endProgress}%`;
                        } else {
                            throw new Error('Compression returned null');
                        }
                    } catch (error) {
                        console.error('Error compressing image:', error);

                        // If compression fails, still try to use original if under 5MB
                        if (file.size <= maxSizeBytes) {
                            compressedFiles.push({
                                original: file,
                                compressed: file,
                                index: i
                            });
                            createImagePreview(file, file, i, context);
                            console.log(`Using original file for ${file.name} (compression failed but file is under size limit)`);
                        } else {
                            const errorMsg = error.message.includes('timeout')
                                ? `Image compression timed out for ${file.name}. Please try a smaller image or different format.`
                                : `Failed to compress ${file.name} to required size (${formatFileSize(maxSizeBytes)}). Current size: ${formatFileSize(file.size)}. Please choose a smaller image.`;

                            alert(errorMsg);
                            console.error(`Compression failed for ${file.name}:`, error);
                        }
                    }
                }
            } finally {
                // Always hide progress bar when done, even if there's an error
                console.log('Finally block reached - hiding progress container for context:', context);

                // Use setTimeout to ensure progress bar hides after async operations complete
                setTimeout(() => {
                    if (progressContainer) {
                        progressContainer.classList.add('hidden');
                        console.log('Progress container successfully hidden for context:', context);
                    } else {
                        console.error('Progress container not found when trying to hide for context:', context);
                    }
                }, 500); // Increased timeout to 500ms
            }
            updateUploadSummary(compressedFiles, context);

            // Update form data
            updateFormData(compressedFiles, context);
        }





        // Optimized compression function for better performance
        async function compressImageToSize(file, maxSizeBytes) {
            try {
                // Start with reasonable dimensions based on file size
                let maxDimension = file.size > 10 * 1024 * 1024 ? 800 : 1200; // 10MB threshold
                let quality = 0.8; // Start with lower quality for faster processing
                let compressedFile;
                let attempts = 0;
                const maxAttempts = 4; // Limit attempts to prevent infinite loops

                // Quick size check - if already small enough, use higher quality
                if (file.size <= maxSizeBytes) {
                    return await compressImage(file, maxDimension, maxDimension, 0.9);
                }

                // Progressive compression with limited attempts
                do {
                    try {
                        compressedFile = await compressImage(file, maxDimension, maxDimension, quality);
                        attempts++;

                        if (compressedFile && compressedFile.size <= maxSizeBytes) {
                            break;
                        }

                        // Adjust parameters more aggressively for faster convergence
                        if (attempts === 1) {
                            quality = 0.6;
                        } else if (attempts === 2) {
                            quality = 0.4;
                            maxDimension = Math.floor(maxDimension * 0.8);
                        } else {
                            maxDimension = Math.floor(maxDimension * 0.7);
                            quality = 0.3;
                        }
                    } catch (error) {
                        console.error(`Compression attempt ${attempts + 1} failed:`, error);
                        // If compression fails, try with more aggressive settings
                        quality = Math.max(0.1, quality - 0.2);
                        maxDimension = Math.floor(maxDimension * 0.8);
                        attempts++;
                    }

                } while ((!compressedFile || compressedFile.size > maxSizeBytes) && attempts < maxAttempts);

                if (!compressedFile) {
                    throw new Error('Failed to compress image after all attempts');
                }

                return compressedFile;
            } catch (error) {
                console.error('Image compression failed:', error);
                throw error;
            }
        }

        // Image compression function with timeout and error handling
        function compressImage(file, maxWidth, maxHeight, quality) {
            return new Promise((resolve, reject) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                let blobUrl = null;

                // Set timeout to prevent hanging
                const timeout = setTimeout(() => {
                    if (blobUrl) URL.revokeObjectURL(blobUrl);
                    reject(new Error('Image compression timeout'));
                }, 30000); // 30 second timeout

                img.onload = () => {
                    try {
                        let { width, height } = calculateDimensions(
                            img.width,
                            img.height,
                            maxWidth,
                            maxHeight
                        );

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);
                        canvas.toBlob((blob) => {
                            clearTimeout(timeout);
                            if (blobUrl) URL.revokeObjectURL(blobUrl);

                            if (blob) {
                                resolve(blob);
                            } else {
                                reject(new Error('Failed to create compressed blob'));
                            }
                        }, 'image/jpeg', quality);
                    } catch (error) {
                        clearTimeout(timeout);
                        if (blobUrl) URL.revokeObjectURL(blobUrl);
                        reject(error);
                    }
                };

                img.onerror = () => {
                    clearTimeout(timeout);
                    if (blobUrl) URL.revokeObjectURL(blobUrl);
                    reject(new Error('Failed to load image for compression'));
                };

                try {
                    blobUrl = URL.createObjectURL(file);
                    img.src = blobUrl;
                } catch (error) {
                    clearTimeout(timeout);
                    reject(error);
                }
            });
        }

        // Calculate new dimensions maintaining aspect ratio
        function calculateDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
            const ratio = Math.min(maxWidth / originalWidth, maxHeight / originalHeight);

            if (ratio >= 1) {
                return { width: originalWidth, height: originalHeight };
            }

            return {
                width: Math.round(originalWidth * ratio),
                height: Math.round(originalHeight * ratio)
            };
        }

        // Drag and drop handlers
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleFileDrop(event, context) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = Array.from(event.dataTransfer.files);
            processFiles(files, context);
        }

        // Create image preview
        function createImagePreview(compressedFile, originalFile, index, context) {
            const previewGrid = document.getElementById(context + 'ImagePreviewGrid');
            const previewDiv = document.createElement('div');
            previewDiv.className = 'image-preview relative';
            previewDiv.dataset.index = index;

            const img = document.createElement('img');
            img.alt = `Preview ${index + 1}`;

            // Use FileReader to create data URL instead of blob URL to avoid CSP issues
            const reader = new FileReader();
            reader.onload = function(e) {
                img.src = e.target.result;
            };
            reader.readAsDataURL(compressedFile);

            const overlay = document.createElement('div');
            overlay.className = 'image-overlay';

            const actions = document.createElement('div');
            actions.className = 'flex space-x-2';

            // Set as display image button
            const displayBtn = document.createElement('button');
            displayBtn.type = 'button';
            displayBtn.className = 'bg-orange-500 text-white px-2 py-1 rounded text-xs hover:bg-orange-600 transition-colors';
            displayBtn.innerHTML = '<i class="fas fa-star"></i>';
            displayBtn.title = 'Set as display image';
            displayBtn.onclick = () => setDisplayImage(index, context);

            // Delete button
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 transition-colors';
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.title = 'Remove image';
            deleteBtn.onclick = () => removeImage(index, context);

            actions.appendChild(displayBtn);
            actions.appendChild(deleteBtn);
            overlay.appendChild(actions);

            // Compression info
            const info = document.createElement('div');
            info.className = 'compression-info mt-1 text-center';
            const originalSize = formatFileSize(originalFile.size);
            const compressedSize = formatFileSize(compressedFile.size);
            const savings = Math.round((1 - compressedFile.size / originalFile.size) * 100);
            info.innerHTML = `${originalSize} → ${compressedSize} (${savings}% saved)`;

            previewDiv.appendChild(img);
            previewDiv.appendChild(overlay);
            previewDiv.appendChild(info);

            // Display image indicator
            const currentDisplayIndex = context === 'add' ? addDisplayImageIndex : editDisplayImageIndex;
            if (index === currentDisplayIndex) {
                const indicator = document.createElement('div');
                indicator.className = 'display-image-indicator';
                indicator.textContent = 'Display';
                previewDiv.appendChild(indicator);
            }

            previewGrid.appendChild(previewDiv);
        }

        // Update upload summary
        function updateUploadSummary(compressedFiles, context) {
            const uploadSummary = document.getElementById(context + 'UploadSummary');
            const totalFilesSpan = document.getElementById(context + 'TotalFiles');
            const totalSizeSpan = document.getElementById(context + 'TotalSize');

            if (compressedFiles.length > 0) {
                const totalSize = compressedFiles.reduce((sum, file) => sum + file.compressed.size, 0);
                totalFilesSpan.textContent = compressedFiles.length;
                totalSizeSpan.textContent = formatFileSize(totalSize);
                uploadSummary.classList.remove('hidden');
            } else {
                uploadSummary.classList.add('hidden');
            }
        }

        // Get all compressed files for a context
        function getAllCompressedFiles(context) {
            // Get the appropriate array based on context
            const selectedFiles = context === 'add' ? addSelectedFiles : editSelectedFiles;

            // Create an array of compressed file objects
            const compressedFiles = [];

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                compressedFiles.push({
                    original: file,
                    compressed: file, // Use original as compressed since we've already compressed them
                    index: i
                });
            }

            return compressedFiles;
        }

        // Update form data with all files (both existing and newly added)
        function updateFormDataWithAllFiles(context) {
            const fileInput = document.getElementById(context === 'add' ? 'addImageInput' : 'editImageInput');
            const dataTransfer = new DataTransfer();
            const selectedFiles = context === 'add' ? addSelectedFiles : editSelectedFiles;

            console.log(`Updating form data for ${context} with ${selectedFiles.length} files`);

            // Add all files to the DataTransfer object
            selectedFiles.forEach((file, index) => {
                dataTransfer.items.add(file);
                console.log(`Added file ${index + 1}: ${file.name} (${formatFileSize(file.size)})`);
            });

            // Update the file input with all files
            fileInput.files = dataTransfer.files;
            console.log(`File input now has ${fileInput.files.length} files`);
        }

        // Update form data with compressed files (legacy function)
        function updateFormData(compressedFiles, context) {
            const fileInput = document.getElementById(context === 'add' ? 'addImageInput' : 'editImageInput');
            const dataTransfer = new DataTransfer();

            compressedFiles.forEach(fileData => {
                dataTransfer.items.add(fileData.compressed);
            });

            fileInput.files = dataTransfer.files;
        }

        // Set display image
        function setDisplayImage(index, context) {
            if (context === 'add') {
                addDisplayImageIndex = index;
                document.getElementById('addDisplayImageIndex').value = index;
            } else {
                editDisplayImageIndex = index;
                document.getElementById('editDisplayImageIndex').value = index;
            }

            // Update visual indicators
            const previewGrid = document.getElementById(context + 'ImagePreviewGrid');
            previewGrid.querySelectorAll('.display-image-indicator').forEach(el => el.remove());
            previewGrid.querySelectorAll('.image-preview').forEach((el, i) => {
                if (i === index) {
                    const indicator = document.createElement('div');
                    indicator.className = 'display-image-indicator';
                    indicator.textContent = 'Display';
                    el.appendChild(indicator);
                }
            });
        }

        // Remove image from selection
        function removeImage(index, context) {
            console.log(`Removing image at index ${index} for context ${context}`);

            if (context === 'add') {
                // Remove the file from the array
                addSelectedFiles.splice(index, 1);

                // Adjust display image index
                if (addDisplayImageIndex === index) {
                    addDisplayImageIndex = -1;
                    document.getElementById('addDisplayImageIndex').value = '';
                } else if (addDisplayImageIndex > index) {
                    addDisplayImageIndex--;
                    document.getElementById('addDisplayImageIndex').value = addDisplayImageIndex;
                }

                // Remove the preview element
                const previewGrid = document.getElementById('addImagePreviewGrid');
                const previewElement = previewGrid.querySelector(`[data-index="${index}"]`);
                if (previewElement) {
                    previewElement.remove();
                }

                // Update indices for remaining previews
                updatePreviewIndices('add');

                // Update form data and summary
                updateFormDataWithAllFiles('add');
                updateUploadSummary(getAllCompressedFiles('add'), 'add');

                // Hide summary if no files left
                if (addSelectedFiles.length === 0) {
                    document.getElementById('addUploadSummary').classList.add('hidden');
                }
            } else {
                // Remove the file from the array
                editSelectedFiles.splice(index, 1);

                // Adjust display image index
                if (editDisplayImageIndex === index) {
                    editDisplayImageIndex = -1;
                    document.getElementById('editDisplayImageIndex').value = '';
                } else if (editDisplayImageIndex > index) {
                    editDisplayImageIndex--;
                    document.getElementById('editDisplayImageIndex').value = editDisplayImageIndex;
                }

                // Remove the preview element
                const previewGrid = document.getElementById('editImagePreviewGrid');
                const previewElement = previewGrid.querySelector(`[data-index="${index}"]`);
                if (previewElement) {
                    previewElement.remove();
                }

                // Update indices for remaining previews
                updatePreviewIndices('edit');

                // Update form data and summary
                updateFormDataWithAllFiles('edit');
                updateUploadSummary(getAllCompressedFiles('edit'), 'edit');

                // Hide summary if no files left
                if (editSelectedFiles.length === 0) {
                    document.getElementById('editUploadSummary').classList.add('hidden');
                }
            }
        }

        // Update preview indices after removing an image
        function updatePreviewIndices(context) {
            const previewGrid = document.getElementById(context + 'ImagePreviewGrid');
            const previews = previewGrid.children;

            for (let i = 0; i < previews.length; i++) {
                const preview = previews[i];
                preview.dataset.index = i;

                // Update the onclick handlers for buttons
                const displayBtn = preview.querySelector('.display-btn');
                const deleteBtn = preview.querySelector('.bg-red-500');

                if (displayBtn) {
                    displayBtn.onclick = () => setDisplayImage(i, context);
                }
                if (deleteBtn) {
                    deleteBtn.onclick = () => removeImage(i, context);
                }
            }
        }

        function loadExistingImages(packageId) {
            fetch(`api/images.php/packages/${packageId}/images`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        existingImages = result.data;
                        displayExistingImages(result.data);
                    }
                })
                .catch(error => {
                    console.error('Error loading existing images:', error);
                });
        }

        // Display existing images
        function displayExistingImages(images) {
            const container = document.getElementById('existingImagesContainer');
            const grid = document.getElementById('existingImagesGrid');

            if (images.length === 0) {
                container.classList.add('hidden');
                return;
            }

            container.classList.remove('hidden');
            grid.innerHTML = '';

            images.forEach(image => {
                const previewDiv = document.createElement('div');
                previewDiv.className = 'relative group';
                previewDiv.setAttribute('data-image-id', image.image_id);

                const img = document.createElement('img');
                img.src = image.url;
                img.alt = image.alt_text || 'Package image';
                img.className = 'w-full h-24 object-cover rounded-lg';

                const overlay = document.createElement('div');
                overlay.className = 'absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center';

                const actions = document.createElement('div');
                actions.className = 'flex space-x-2';

                // Set as display image button
                const displayBtn = document.createElement('button');
                displayBtn.type = 'button';
                displayBtn.className = 'bg-orange-500 text-white px-2 py-1 rounded text-xs hover:bg-orange-600 transition-colors';
                displayBtn.innerHTML = '<i class="fas fa-star"></i>';
                displayBtn.title = 'Set as display image';
                displayBtn.onclick = () => setExistingDisplayImageForPackage(image.image_id);

                // Delete button
                const deleteBtn = document.createElement('button');
                deleteBtn.type = 'button';
                deleteBtn.className = 'bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 transition-colors';
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                deleteBtn.title = 'Delete image';
                deleteBtn.onclick = () => deleteExistingImageForPackage(image.image_id, previewDiv);

                actions.appendChild(displayBtn);
                actions.appendChild(deleteBtn);
                overlay.appendChild(actions);

                previewDiv.appendChild(img);
                previewDiv.appendChild(overlay);

                // Display image indicator
                if (image.is_display_image) {
                    const indicator = document.createElement('div');
                    indicator.className = 'display-image-indicator';
                    indicator.textContent = 'Display';
                    previewDiv.appendChild(indicator);
                }

                grid.appendChild(previewDiv);
            });
        }



        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Delete existing image
        function deleteExistingImage(imageId, packageId) {
            if (!confirm('Are you sure you want to delete this image?')) {
                return;
            }

            fetch('packages.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=delete_image&image_id=${imageId}&tour_package_id=${packageId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the image element from the DOM
                    const imageElement = document.querySelector(`[data-image-id="${imageId}"]`);
                    if (imageElement) {
                        imageElement.remove();
                    }

                    // Show success message
                    alert('Image deleted successfully!');
                } else {
                    alert('Failed to delete image: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the image.');
            });
        }

        // Set existing image as display image for package
        function setExistingDisplayImageForPackage(imageId) {
            const packageId = document.getElementById('editPackageId').value;

            fetch('packages.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=set_display_image&image_id=${imageId}&tour_package_id=${packageId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update visual indicators
                    document.querySelectorAll('#existingImagesGrid .display-image-indicator').forEach(el => el.remove());
                    const imageElement = document.querySelector(`[data-image-id="${imageId}"]`);
                    if (imageElement) {
                        const indicator = document.createElement('div');
                        indicator.className = 'display-image-indicator';
                        indicator.textContent = 'Display';
                        imageElement.appendChild(indicator);
                    }
                    alert('Display image updated successfully!');
                } else {
                    alert('Failed to set display image: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error setting display image');
            });
        }

        // Delete existing image for package (improved version)
        function deleteExistingImageForPackage(imageId, previewElement) {
            if (!confirm('Are you sure you want to delete this image?')) {
                return;
            }

            const packageId = document.getElementById('editPackageId').value;

            fetch('packages.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=delete_image&image_id=${imageId}&tour_package_id=${packageId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the image element from the DOM
                    previewElement.remove();

                    // Update existingImages array
                    existingImages = existingImages.filter(img => img.image_id != imageId);

                    // Hide container if no images left
                    if (existingImages.length === 0) {
                        document.getElementById('existingImagesContainer').classList.add('hidden');
                    }

                    alert('Image deleted successfully!');
                } else {
                    alert('Failed to delete image: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error deleting image');
            });
        }
    </script>

    <!-- Quill Rich Text Editor -->
    <link href="https://cdn.jsdelivr.net/npm/quill@1.3.6/dist/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/quill@1.3.6/dist/quill.min.js"></script>

    <style>
        /* Ensure Quill toolbar is visible */
        .ql-toolbar {
            border: 1px solid #ccc !important;
            border-bottom: none !important;
            background: #f8f9fa !important;
        }
        .ql-container {
            border: 1px solid #ccc !important;
            font-size: 14px !important;
            height: 250px !important;
        }
        .ql-editor {
            height: 200px !important;
            max-height: 200px !important;
            overflow-y: auto !important;
            padding: 12px !important;
        }
    </style>

    <script>
        let addQuill, editQuill;

        // Function to initialize Quill editors with retry logic
        function initializeQuillEditors() {
            console.log('Attempting to initialize Quill editors...');

            if (typeof Quill === 'undefined') {
                console.log('Quill not loaded yet, retrying in 500ms...');
                setTimeout(initializeQuillEditors, 500);
                return;
            }

            console.log('Quill is available, initializing editors...');

            // Initialize Add Itinerary Editor
            const addTextarea = document.getElementById('addItinerary');
            if (addTextarea && !addQuill) {
                console.log('Initializing Add Itinerary editor...');

                // Create Quill container
                const addContainer = document.createElement('div');
                addContainer.id = 'addItineraryEditor';
                addContainer.style.marginTop = '8px';
                addTextarea.parentNode.insertBefore(addContainer, addTextarea.nextSibling);

                // Hide original textarea
                addTextarea.style.display = 'none';

                try {
                    addQuill = new Quill('#addItineraryEditor', {
                        theme: 'snow',
                        modules: {
                            toolbar: [
                                [{ 'header': [1, 2, 3, false] }],
                                ['bold', 'italic', 'underline'],
                                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                                ['link'],
                                ['clean']
                            ]
                        },
                        placeholder: 'Add detailed day-by-day itinerary, activities, schedule, etc.'
                    });

                    // Sync Quill content with textarea
                    addQuill.on('text-change', function() {
                        addTextarea.value = addQuill.root.innerHTML;
                    });

                    console.log('✅ Add Quill editor initialized successfully');
                } catch (error) {
                    console.error('❌ Error initializing Add Quill editor:', error);
                }
            }

            // Initialize Edit Itinerary Editor
            const editTextarea = document.getElementById('editItinerary');
            if (editTextarea && !editQuill) {
                console.log('Initializing Edit Itinerary editor...');

                // Create Quill container
                const editContainer = document.createElement('div');
                editContainer.id = 'editItineraryEditor';
                editContainer.style.marginTop = '8px';
                editTextarea.parentNode.insertBefore(editContainer, editTextarea.nextSibling);

                // Hide original textarea
                editTextarea.style.display = 'none';

                try {
                    editQuill = new Quill('#editItineraryEditor', {
                        theme: 'snow',
                        modules: {
                            toolbar: [
                                [{ 'header': [1, 2, 3, false] }],
                                ['bold', 'italic', 'underline'],
                                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                                ['link'],
                                ['clean']
                            ]
                        },
                        placeholder: 'Add detailed day-by-day itinerary, activities, schedule, etc.'
                    });

                    // Sync Quill content with textarea
                    editQuill.on('text-change', function() {
                        editTextarea.value = editQuill.root.innerHTML;
                    });

                    console.log('✅ Edit Quill editor initialized successfully');
                } catch (error) {
                    console.error('❌ Error initializing Edit Quill editor:', error);
                }
            }
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeQuillEditors);
        } else {
            initializeQuillEditors();
        }

        // Also try to initialize after a short delay (fallback)
        setTimeout(initializeQuillEditors, 1000);
    </script>
</body>
</html>
